Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBAD3E0000 ntdll.dll
7FFB8A060000 aswhook.dll
7FFBACBA0000 KERNEL32.DLL
7FFBAA6B0000 KERNELBASE.dll
7FFBAC920000 USER32.dll
7FFBAAD30000 win32u.dll
7FFBACD00000 GDI32.dll
7FFBAABF0000 gdi32full.dll
7FFBAAD60000 msvcp_win.dll
7FFBAAEB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBAC300000 advapi32.dll
7FFBABEA0000 msvcrt.dll
7FFBACAF0000 sechost.dll
7FFBAD280000 RPCRT4.dll
7FFBA9B10000 CRYPTBASE.DLL
7FFBAAE10000 bcryptPrimitives.dll
7FFBAC8E0000 IMM32.DLL
