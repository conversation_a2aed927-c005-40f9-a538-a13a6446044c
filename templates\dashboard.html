<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Employee Dashboard</title>
  <link rel="stylesheet" href="../public/css/style.css" />

  <script>
    // Employee data
    window.employeeData = [
      { id: 1, firstName: "<PERSON>", lastName: "Doe", email: "<EMAIL>", department: "Engineering", role: "Senior Developer" },
      { id: 2, firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", department: "HR", role: "HR Manager" },
      { id: 3, firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", department: "Marketing", role: "Marketing Specialist" },
      { id: 4, firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", department: "Engineering", role: "Frontend Developer" },
      { id: 5, firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", department: "Sales", role: "Sales Representative" },
      { id: 6, firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", department: "Finance", role: "Financial Analyst" },
      { id: 7, firstName: "Robert", lastName: "<PERSON>", email: "<EMAIL>", department: "Engineering", role: "Backend Developer" },
      { id: 8, firstName: "Lisa", lastName: "<PERSON>", email: "<EMAIL>", department: "HR", role: "HR Coordinator" },
      { id: 9, firstName: "James", lastName: "Moore", email: "<EMAIL>", department: "Marketing", role: "Content Creator" },
      { id: 10, firstName: "Jennifer", lastName: "Taylor", email: "<EMAIL>", department: "Sales", role: "Sales Manager" },
      { id: 11, firstName: "Christopher", lastName: "Anderson", email: "<EMAIL>", department: "Finance", role: "Accountant" },
      { id: 12, firstName: "Amanda", lastName: "Thomas", email: "<EMAIL>", department: "Engineering", role: "DevOps Engineer" }
    ];

    // Extract unique departments and roles
    window.departments = [...new Set(window.employeeData.map(emp => emp.department))];
    window.roles = [...new Set(window.employeeData.map(emp => emp.role))];
  </script>
</head>

<body>
  <header class="header">
    <div class="header-top">
      <h1>Employee directory</h1>
      <button class="btn btn-primary add-btn" onclick="window.location.href='form.html'">
        <span class="btn-icon">+</span> Add Employee
      </button>
    </div>

    <div class="controls">
      <div class="search-section">
        <input type="text" id="searchInput" placeholder="Search by name or email..." class="search-input" />
        <button id="filterToggle" class="btn btn-secondary">
          <span class="btn-icon">⚙</span> Filters
        </button>
      </div>

      <div class="view-controls">
        <div class="view-toggle">
          <button id="gridView" class="view-btn active" title="Grid View">⊞</button>
          <button id="tableView" class="view-btn" title="Table View">☰</button>
        </div>
        <select id="itemsPerPage" class="select-input">
          <option value="10">10 per page</option>
          <option value="25">25 per page</option>
          <option value="50">50 per page</option>
          <option value="100">100 per page</option>
        </select>
      </div>
    </div>
  </header>

  <!-- Filter Sidebar -->
  <div id="filterSidebar" class="filter-sidebar">
    <div class="filter-header">
      <h3>Filters</h3>
      <button id="closeFilters" class="btn-close">&times;</button>
    </div>

    <div class="filter-group">
      <label for="firstNameFilter">First Name:</label>
      <input type="text" id="firstNameFilter" class="filter-input" />
    </div>

    <div class="filter-group">
      <label for="departmentFilter">Department:</label>
      <select id="departmentFilter" class="filter-select">
        <option value="">All Departments</option>
      </select>
    </div>

    <div class="filter-group">
      <label for="roleFilter">Role:</label>
      <select id="roleFilter" class="filter-select">
        <option value="">All Roles</option>
      </select>
    </div>

    <div class="filter-group">
      <label for="sortBy">Sort By:</label>
      <select id="sortBy" class="filter-select">
        <option value="firstName">First Name</option>
        <option value="lastName">Last Name</option>
        <option value="department">Department</option>
        <option value="role">Role</option>
      </select>
    </div>

    <div class="filter-actions">
      <button id="clearFilters" class="btn btn-secondary">Clear All</button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="main-content">
    <div class="content-header">
      <div id="resultCount" class="result-count"></div>
      <div class="loading" id="loading" style="display: none;">Loading...</div>
    </div>

    <div id="employeeContainer" class="employee-container"></div>
    <div id="pagination" class="pagination"></div>
  </main>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Confirm Delete</h3>
        <button class="btn-close" onclick="window.employeeDirectory.closeDeleteModal()">&times;</button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this employee?</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-danger" onclick="window.employeeDirectory.confirmDelete()">Delete</button>
        <button class="btn btn-secondary" onclick="window.employeeDirectory.closeDeleteModal()">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Overlay -->
  <div id="filterOverlay" class="filter-overlay"></div>

  <script>
    // Populate Department and Role Filters
    window.addEventListener("DOMContentLoaded", () => {
      const deptSelect = document.getElementById("departmentFilter");
      window.departments.forEach(dept => {
        const opt = document.createElement("option");
        opt.value = dept;
        opt.textContent = dept;
        deptSelect.appendChild(opt);
      });

      const roleSelect = document.getElementById("roleFilter");
      window.roles.forEach(role => {
        const opt = document.createElement("option");
        opt.value = role;
        opt.textContent = role;
        roleSelect.appendChild(opt);
      });
    });
  </script>

  <script src="../public/js/dashboard.js"></script>
</body>
</html>
