<html>
<head>
  <title>Employee Dashboard</title>
  <link rel="stylesheet" href="../public/css/style.css">
  <script>
  window.employeeData = [
    {
      id: 1,
      firstName: "<PERSON>",
      lastName: "<PERSON><PERSON>",
      email: "<EMAIL>",
      department: "Engineering",
      role: "Developer"
    },
    {
      id: 2,
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      department: "HR",
      role: "Manager"
    }
  ];
</script>

</head>
<body>
  <header>
    <h1>Employee Directory</h1>
    <input type="text" id="searchInput" placeholder="Search by name or email">
    <select id="departmentFilter">
      <option value="">All Departments</option>
      <option value="Engineering">Engineering</option>
      <option value="HR">HR</option>
    </select>
    <select id="roleFilter">
      <option value="">All Roles</option>
      <option value="Developer">Developer</option>
      <option value="Manager">Manager</option>
    </select>
    <select id="sortBy">
      <option value="firstName">Sort by First Name</option>
      <option value="department">Sort by Department</option>
    </select>
    <select id="itemsPerPage">
      <option value="10">10</option>
      <option value="25">25</option>
      <option value="50">50</option>
    </select>
    <button onclick="window.location.href='form.ftl'">Add Employee</button>
  </header>

  <div id="employeeContainer"></div>
  <div id="pagination"></div>
  <div id="resultCount"></div>

  <div id="deleteModal" class="modal">
    <div class="modal-content">
      <p>Are you sure you want to delete this employee?</p>
      <button onclick="window.employeeDirectory.confirmDelete()">Yes</button>
      <button onclick="window.employeeDirectory.closeDeleteModal()">Cancel</button>
    </div>
  </div>

  <script src="../public/js/dashboard.js"></script>
</body>
</html>