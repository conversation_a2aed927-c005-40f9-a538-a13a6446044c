document.getElementById('employeeForm').addEventListener('submit', function (e) {
  e.preventDefault();

  const newEmployee = {
    id: Date.now(),
    firstName: document.getElementById('firstName').value,
    lastName: document.getElementById('lastName').value,
    email: document.getElementById('email').value,
    department: document.getElementById('department').value,
    role: document.getElementById('role').value,
  };

  const employees = JSON.parse(localStorage.getItem('employeeDirectory')) || [];

  // Check if edit
  const urlParams = new URLSearchParams(window.location.search);
  const editId = urlParams.get('id');
  if (editId) {
    const idx = employees.findIndex(e => e.id == editId);
    employees[idx] = { ...newEmployee, id: parseInt(editId) };
  } else {
    employees.push(newEmployee);
  }

  localStorage.setItem('employeeDirectory', JSON.stringify(employees));
  window.location.href = 'dashboard.ftl';
});