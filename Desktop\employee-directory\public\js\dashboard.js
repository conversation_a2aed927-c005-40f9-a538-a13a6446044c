/**
 * Employee Directory Dashboard JavaScript
 * Handles filtering, sorting, pagination, and view management
 */

class EmployeeDirectory {
  constructor() {
    this.employees = window.employeeData || [];
    this.filteredEmployees = [...this.employees];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.currentView = "grid";
    this.deleteEmployeeId = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadFromLocalStorage();
    this.renderEmployees();
    this.updateResultCount();
  }

  setupEventListeners() {
    document.getElementById("searchInput").addEventListener("input", this.debounce(this.handleSearch.bind(this), 300));
    document.getElementById("departmentFilter").addEventListener("change", this.handleFilter.bind(this));
    document.getElementById("roleFilter").addEventListener("change", this.handleFilter.bind(this));
    document.getElementById("sortBy").addEventListener("change", this.handleSort.bind(this));
    document.getElementById("gridView").addEventListener("click", () => this.setView("grid"));
    document.getElementById("tableView").addEventListener("click", () => this.setView("table"));
    document.getElementById("itemsPerPage").addEventListener("change", this.handleItemsPerPageChange.bind(this));
    document.getElementById("deleteModal").addEventListener("click", (e) => {
      if (e.target.id === "deleteModal") {
        this.closeDeleteModal();
      }
    });
    document.addEventListener("keydown", this.handleKeyboardShortcuts.bind(this));
  }

  debounce(func, delay) {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), delay);
    };
  }

  loadFromLocalStorage() {
    const data = localStorage.getItem("employeeDirectory");
    if (data) {
      this.employees = JSON.parse(data);
      this.filteredEmployees = [...this.employees];
    }
  }

  saveToLocalStorage() {
    localStorage.setItem("employeeDirectory", JSON.stringify(this.employees));
  }

  handleSearch() {
    const value = document.getElementById("searchInput").value.toLowerCase().trim();
    this.filteredEmployees = this.employees.filter((emp) => {
      const fullName = `${emp.firstName} ${emp.lastName}`.toLowerCase();
      return fullName.includes(value) || emp.email.toLowerCase().includes(value);
    });
    this.currentPage = 1;
    this.renderEmployees();
    this.updateResultCount();
  }

  handleFilter() {
    const department = document.getElementById("departmentFilter").value;
    const role = document.getElementById("roleFilter").value;
    const search = document.getElementById("searchInput").value.toLowerCase().trim();

    this.filteredEmployees = this.employees.filter((emp) => {
      const matchSearch =
        search === "" ||
        `${emp.firstName} ${emp.lastName}`.toLowerCase().includes(search) ||
        emp.email.toLowerCase().includes(search);
      const matchDept = department === "" || emp.department === department;
      const matchRole = role === "" || emp.role === role;
      return matchSearch && matchDept && matchRole;
    });

    this.applySort();
    this.currentPage = 1;
    this.renderEmployees();
    this.updateResultCount();
  }

  handleSort() {
    this.applySort();
    this.renderEmployees();
  }

  applySort() {
    const sortBy = document.getElementById("sortBy").value;
    this.filteredEmployees.sort((a, b) => {
      const valA = a[sortBy].toLowerCase();
      const valB = b[sortBy].toLowerCase();
      return valA.localeCompare(valB);
    });
  }

  handleItemsPerPageChange() {
    this.itemsPerPage = parseInt(document.getElementById("itemsPerPage").value);
    this.currentPage = 1;
    this.renderEmployees();
  }

  setView(view) {
    this.currentView = view;
    document.querySelectorAll(".view-btn").forEach((btn) => btn.classList.remove("active"));
    document.getElementById(view + "View").classList.add("active");
    this.renderEmployees();
  }

  getPaginatedEmployees() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    return this.filteredEmployees.slice(start, start + this.itemsPerPage);
  }

  renderEmployees() {
    const container = document.getElementById("employeeContainer");
    const employees = this.getPaginatedEmployees();

    if (this.filteredEmployees.length === 0) {
      container.innerHTML = `<div class="empty">No employees found</div>`;
      return;
    }

    if (this.currentView === "grid") {
      container.className = "employee-grid";
      container.innerHTML = employees.map((e) => this.createEmployeeCard(e)).join("");
    } else {
      container.className = "employee-table";
      container.innerHTML = this.createEmployeeTable(employees);
    }

    this.renderPagination();
  }

  createEmployeeCard(emp) {
    return `
      <div class="card">
        <h3>${emp.firstName} ${emp.lastName}</h3>
        <p>ID: ${emp.id}</p>
        <p>${emp.email}</p>
        <p>${emp.department} | ${emp.role}</p>
        <button onclick="window.employeeDirectory.editEmployee(${emp.id})">Edit</button>
        <button onclick="window.employeeDirectory.showDeleteModal(${emp.id})">Delete</button>
      </div>
    `;
  }

  createEmployeeTable(emps) {
    return `
      <table>
        <thead>
          <tr>
            <th>ID</th><th>Name</th><th>Email</th><th>Department</th><th>Role</th><th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${emps
            .map(
              (e) => `
            <tr>
              <td>${e.id}</td>
              <td>${e.firstName} ${e.lastName}</td>
              <td>${e.email}</td>
              <td>${e.department}</td>
              <td>${e.role}</td>
              <td>
                <button onclick="window.employeeDirectory.editEmployee(${e.id})">Edit</button>
                <button onclick="window.employeeDirectory.showDeleteModal(${e.id})">Delete</button>
              </td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>
    `;
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredEmployees.length / this.itemsPerPage);
    const container = document.getElementById("pagination");

    if (totalPages <= 1) return (container.innerHTML = "");

    let buttons = "";

    for (let i = 1; i <= totalPages; i++) {
      buttons += `<button class="${this.currentPage === i ? "active" : ""}" onclick="window.employeeDirectory.goToPage(${i})">${i}</button>`;
    }

    container.innerHTML = buttons;
  }

  goToPage(page) {
    this.currentPage = page;
    this.renderEmployees();
  }

  updateResultCount() {
    const count = document.getElementById("resultCount");
    const total = this.filteredEmployees.length;
    const start = (this.currentPage - 1) * this.itemsPerPage + 1;
    const end = Math.min(start + this.itemsPerPage - 1, total);
    count.textContent = `Showing ${start}-${end} of ${total}`;
  }

  editEmployee(id) {
    window.location.href = `form.ftl?id=${id}`;
  }

  showDeleteModal(id) {
    this.deleteEmployeeId = id;
    document.getElementById("deleteModal").style.display = "block";
  }

  closeDeleteModal() {
    this.deleteEmployeeId = null;
    document.getElementById("deleteModal").style.display = "none";
  }

  confirmDelete() {
    if (this.deleteEmployeeId) {
      this.employees = this.employees.filter((emp) => emp.id !== this.deleteEmployeeId);
      this.saveToLocalStorage();
      this.handleSearch();
      this.closeDeleteModal();
    }
  }

  handleKeyboardShortcuts(e) {
    if (e.key === "Escape") this.closeDeleteModal();
  }
}

document.addEventListener("DOMContentLoaded", () => {
  window.employeeDirectory = new EmployeeDirectory();
});
