<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Employee Dashboard - Test Version</title>
  <link rel="stylesheet" href="public/css/style.css">
  
  <script>
    // Mock employee data for testing
    window.employeeData = [
      {
        id: 1,
        firstName: "<PERSON>",
        lastName: "<PERSON><PERSON>", 
        email: "<EMAIL>",
        department: "Engineering",
        role: "Senior Developer"
      },
      {
        id: 2,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>", 
        department: "HR",
        role: "HR Manager"
      },
      {
        id: 3,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        department: "Marketing",
        role: "Marketing Specialist"
      },
      {
        id: 4,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        department: "Engineering", 
        role: "Frontend Developer"
      },
      {
        id: 5,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        department: "Sales",
        role: "Sales Representative"
      },
      {
        id: 6,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        department: "Finance",
        role: "Financial Analyst"
      },
      {
        id: 7,
        firstName: "Robert",
        lastName: "Miller",
        email: "<EMAIL>",
        department: "Engineering",
        role: "Backend Developer"
      },
      {
        id: 8,
        firstName: "Lisa",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "HR Coordinator"
      },
      {
        id: 9,
        firstName: "James",
        lastName: "Moore",
        email: "<EMAIL>",
        department: "Marketing",
        role: "Content Creator"
      },
      {
        id: 10,
        firstName: "Jennifer",
        lastName: "Taylor",
        email: "<EMAIL>",
        department: "Sales",
        role: "Sales Manager"
      },
      {
        id: 11,
        firstName: "Christopher",
        lastName: "Anderson",
        email: "<EMAIL>",
        department: "Finance",
        role: "Accountant"
      },
      {
        id: 12,
        firstName: "Amanda",
        lastName: "Thomas",
        email: "<EMAIL>",
        department: "Engineering",
        role: "DevOps Engineer"
      }
    ];
  </script>
</head>
<body>
  <header class="header">
    <div class="header-top">
      <h1>Employee Directory</h1>
      <button class="btn btn-primary add-btn" onclick="window.location.href='test-form.html'">
        <span class="btn-icon">+</span> Add Employee
      </button>
    </div>
    
    <div class="controls">
      <div class="search-section">
        <input type="text" id="searchInput" placeholder="Search by name or email..." class="search-input">
        <button id="filterToggle" class="btn btn-secondary">
          <span class="btn-icon">⚙</span> Filters
        </button>
      </div>
      
      <div class="view-controls">
        <div class="view-toggle">
          <button id="gridView" class="view-btn active" title="Grid View">
            <span class="btn-icon">⊞</span>
          </button>
          <button id="tableView" class="view-btn" title="Table View">
            <span class="btn-icon">☰</span>
          </button>
        </div>
        
        <select id="itemsPerPage" class="select-input">
          <option value="10">10 per page</option>
          <option value="25">25 per page</option>
          <option value="50">50 per page</option>
          <option value="100">100 per page</option>
        </select>
      </div>
    </div>
  </header>

  <!-- Filter Sidebar -->
  <div id="filterSidebar" class="filter-sidebar">
    <div class="filter-header">
      <h3>Filters</h3>
      <button id="closeFilters" class="btn-close">&times;</button>
    </div>
    
    <div class="filter-group">
      <label for="firstNameFilter">First Name:</label>
      <input type="text" id="firstNameFilter" placeholder="Filter by first name..." class="filter-input">
    </div>
    
    <div class="filter-group">
      <label for="departmentFilter">Department:</label>
      <select id="departmentFilter" class="filter-select">
        <option value="">All Departments</option>
        <option value="Engineering">Engineering</option>
        <option value="HR">HR</option>
        <option value="Marketing">Marketing</option>
        <option value="Sales">Sales</option>
        <option value="Finance">Finance</option>
      </select>
    </div>
    
    <div class="filter-group">
      <label for="roleFilter">Role:</label>
      <select id="roleFilter" class="filter-select">
        <option value="">All Roles</option>
        <option value="Senior Developer">Senior Developer</option>
        <option value="HR Manager">HR Manager</option>
        <option value="Marketing Specialist">Marketing Specialist</option>
        <option value="Frontend Developer">Frontend Developer</option>
        <option value="Sales Representative">Sales Representative</option>
        <option value="Financial Analyst">Financial Analyst</option>
        <option value="Backend Developer">Backend Developer</option>
        <option value="HR Coordinator">HR Coordinator</option>
        <option value="Content Creator">Content Creator</option>
        <option value="Sales Manager">Sales Manager</option>
        <option value="Accountant">Accountant</option>
        <option value="DevOps Engineer">DevOps Engineer</option>
      </select>
    </div>
    
    <div class="filter-group">
      <label for="sortBy">Sort By:</label>
      <select id="sortBy" class="filter-select">
        <option value="firstName">First Name</option>
        <option value="lastName">Last Name</option>
        <option value="department">Department</option>
        <option value="role">Role</option>
      </select>
    </div>
    
    <div class="filter-actions">
      <button id="clearFilters" class="btn btn-secondary">Clear All</button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="main-content">
    <div class="content-header">
      <div id="resultCount" class="result-count"></div>
      <div class="loading" id="loading" style="display: none;">Loading...</div>
    </div>
    
    <div id="employeeContainer" class="employee-container"></div>
    
    <div id="pagination" class="pagination"></div>
  </main>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Confirm Delete</h3>
        <button class="btn-close" onclick="window.employeeDirectory.closeDeleteModal()">&times;</button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this employee? This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-danger" onclick="window.employeeDirectory.confirmDelete()">Delete</button>
        <button class="btn btn-secondary" onclick="window.employeeDirectory.closeDeleteModal()">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Filter Overlay -->
  <div id="filterOverlay" class="filter-overlay"></div>

  <!-- Message Container -->
  <div id="messageContainer" class="message-container"></div>

  <script src="public/js/dashboard.js"></script>
</body>
</html>
