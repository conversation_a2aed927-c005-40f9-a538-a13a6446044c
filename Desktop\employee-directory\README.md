# Employee Directory Web Application

A responsive and interactive Employee Directory built with HTML, CSS, JavaScript, and Freemarker templates.

## 🚀 Features

### Dashboard
- **Employee Display**: Grid and table view options
- **Search**: Real-time search by name or email
- **Advanced Filtering**: Filter by first name, department, and role
- **Sorting**: Sort by first name, last name, department, or role
- **Pagination**: Configurable items per page (10, 25, 50, 100)
- **Responsive Design**: Works on desktop, tablet, and mobile devices

### Employee Management
- **Add Employee**: Comprehensive form with validation
- **Edit Employee**: Modify existing employee information
- **Delete Employee**: Confirmation modal for safe deletion
- **Form Validation**: Real-time validation with error messages

### User Experience
- **Filter Sidebar**: Slide-out panel for advanced filtering
- **Keyboard Shortcuts**: 
  - `Ctrl/Cmd + F`: Focus search
  - `Ctrl/Cmd + N`: Add new employee
  - `Ctrl/Cmd + S`: Save form (on form page)
  - `Escape`: Close modals/sidebar
- **Success/Error Messages**: Toast notifications for user feedback
- **Loading States**: Visual feedback during operations

## 📁 Project Structure

```
employee-directory/
├── templates/
│   ├── dashboard.ftl          # Main dashboard Freemarker template
│   └── form.ftl              # Employee form Freemarker template
├── public/
│   ├── css/
│   │   └── style.css         # Complete responsive styling
│   └── js/
│       ├── dashboard.js      # Dashboard functionality
│       └── form.js          # Form validation and handling
├── data/
│   └── employeeData.ftl     # Mock data template
├── test-dashboard.html      # HTML test version
├── test-form.html          # HTML test version
├── index.html              # Entry point with redirect
└── README.md              # This file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern responsive design with CSS Grid and Flexbox
- **Vanilla JavaScript**: ES6+ features, modular class-based architecture
- **Freemarker**: Server-side templating for dynamic content
- **Local Storage**: Client-side data persistence

## 🎨 Design Features

### Responsive Design
- Mobile-first approach
- Breakpoints at 768px and 480px
- Flexible grid layouts
- Touch-friendly interface

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- High contrast mode support
- Screen reader friendly
- Focus management

### Modern UI
- Clean, professional design
- Consistent color scheme
- Smooth animations and transitions
- Loading states and feedback
- Error handling with user-friendly messages

## 🚦 Getting Started

### For Testing (HTML Version)
1. Open `test-dashboard.html` in a web browser
2. The application will work with mock data stored in localStorage

### For Production (Freemarker Version)
1. Set up a Java web server (Tomcat, Jetty, etc.)
2. Configure Freemarker template processing
3. Place templates in the appropriate directory
4. Access via `dashboard.ftl`

### Mock Data
The application includes 12 sample employees across different departments:
- Engineering (4 employees)
- HR (2 employees)
- Marketing (2 employees)
- Sales (2 employees)
- Finance (2 employees)

## 📱 Responsive Breakpoints

- **Desktop**: > 768px - Full layout with sidebar
- **Tablet**: 768px - Stacked layout, full-width sidebar
- **Mobile**: < 480px - Single column, optimized touch targets

## ✨ Key Features Implementation

### Filtering System
- **Search Bar**: Searches across name and email fields
- **First Name Filter**: Dedicated input for first name filtering
- **Department Filter**: Dropdown with all available departments
- **Role Filter**: Dropdown with all available roles
- **Clear Filters**: Reset all filters with one click

### Form Validation
- **Required Fields**: All fields are mandatory
- **Email Validation**: Regex pattern validation
- **Duplicate Email Check**: Prevents duplicate email addresses
- **Name Validation**: Allows only letters, spaces, hyphens, apostrophes
- **Real-time Feedback**: Validation on blur and input events

### Data Management
- **Local Storage**: Persistent data storage
- **CRUD Operations**: Create, Read, Update, Delete employees
- **Data Integrity**: Validation and error handling
- **State Management**: Maintains filters and view preferences

## 🎯 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📝 Code Quality

- **Modular Architecture**: Separate classes for different functionality
- **Clean Code**: Well-commented and documented
- **Error Handling**: Comprehensive error management
- **Performance**: Debounced search, efficient DOM manipulation
- **Security**: HTML escaping to prevent XSS

## 🔧 Customization

### Adding New Departments
Update the department options in:
- `templates/form.ftl` (select options)
- `templates/dashboard.ftl` (filter options)

### Styling Customization
Modify CSS custom properties in `style.css`:
```css
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  /* ... other variables */
}
```

### Adding New Fields
1. Add form field in `form.ftl`
2. Update validation in `form.js`
3. Modify display in `dashboard.js`
4. Update CSS as needed

## 🐛 Known Issues

- Freemarker templates require server-side processing
- Local storage has browser limits (typically 5-10MB)
- Print styles optimized for standard paper sizes

## 🚀 Future Enhancements

- Export to CSV/PDF functionality
- Advanced search with multiple criteria
- Bulk operations (delete, edit)
- Employee photos and additional fields
- Integration with backend APIs
- User authentication and authorization

## 📄 License

This project is created for educational/assignment purposes.
